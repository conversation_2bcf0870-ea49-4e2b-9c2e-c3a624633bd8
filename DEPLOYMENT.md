# Cloudflare Pages 部署指南

本项目已配置为可以轻松部署到 Cloudflare Pages。

## 自动部署（推荐）

### 1. 通过 Git 集成部署

1. 将代码推送到 GitHub/GitLab 仓库
2. 登录 [Cloudflare Dashboard](https://dash.cloudflare.com/)
3. 进入 "Pages" 部分
4. 点击 "Create a project"
5. 选择 "Connect to Git"
6. 选择你的仓库
7. 配置构建设置：
   - **Framework preset**: Vue
   - **Build command**: `npm run build`
   - **Build output directory**: `dist`
   - **Root directory**: `/` (如果项目在根目录)

### 2. 环境变量（如果需要）

在 Cloudflare Pages 项目设置中添加环境变量：
- `NODE_VERSION`: `20` (推荐使用 Node.js 20)

## 手动部署

### 1. 安装 Wrangler CLI

```bash
npm install -g wrangler
```

### 2. 登录 Cloudflare

```bash
wrangler login
```

### 3. 构建并部署

```bash
# 构建项目
npm run build

# 部署到 Cloudflare Pages
npm run deploy
```

### 4. 预览部署

```bash
# 部署预览版本
npm run deploy:preview
```

## 配置文件说明

### `wrangler.toml`
- Cloudflare Pages 的主要配置文件
- 定义了构建命令、输出目录和安全头

### `public/_redirects`
- 处理 SPA 路由重定向
- 确保所有路由都由 Vue Router 处理

### `public/_headers`
- 设置安全和性能相关的 HTTP 头
- 配置静态资源缓存策略

## 性能优化

项目已包含以下优化：

1. **代码分割**: 自动分离 vendor 和 i18n 代码块
2. **资源压缩**: 使用 Terser 压缩 JavaScript
3. **缓存策略**: 静态资源长期缓存，HTML 短期缓存
4. **安全头**: 防止 XSS、点击劫持等攻击

## 自定义域名

1. 在 Cloudflare Pages 项目中点击 "Custom domains"
2. 添加你的域名
3. 按照提示配置 DNS 记录

## 监控和分析

Cloudflare Pages 提供：
- 实时访问统计
- 性能监控
- 错误日志
- 部署历史

## 故障排除

### 构建失败
- 检查 Node.js 版本是否为 20+
- 确保所有依赖都已正确安装
- 查看构建日志中的错误信息

### 路由问题
- 确保 `public/_redirects` 文件存在
- 检查 Vue Router 配置

### 静态资源加载失败
- 检查 `vite.config.js` 中的 `base` 配置
- 确保资源路径正确

## 更新部署

当你推送新代码到连接的 Git 仓库时，Cloudflare Pages 会自动触发新的部署。

## 成本

Cloudflare Pages 提供慷慨的免费额度：
- 每月 100,000 次请求
- 无限带宽
- 全球 CDN
- 自动 HTTPS

对于大多数个人和小型项目来说，免费额度已经足够使用。
