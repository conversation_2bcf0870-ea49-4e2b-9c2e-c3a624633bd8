import { defineStore } from 'pinia'
import { ref } from 'vue'

let idCounter = 0

export const useUiStore = defineStore('ui', () => {
  const isGlobalLoading = ref(false)
  const toasts = ref([]) // { id, type: 'info'|'success'|'error', message, timeoutId }

  const removeToast = (id) => {
    const idx = toasts.value.findIndex(t => t.id === id)
    if (idx !== -1) {
      const t = toasts.value[idx]
      if (t.timeoutId) clearTimeout(t.timeoutId)
      toasts.value.splice(idx, 1)
    }
  }

  const showToast = (message, type = 'info', duration = 3000) => {
    const id = ++idCounter
    const toast = { id, type, message, timeoutId: null }
    if (duration > 0) {
      toast.timeoutId = setTimeout(() => removeToast(id), duration)
    }
    toasts.value.push(toast)
    return id
  }

  const showError = (message, duration = 3500) => showToast(message, 'error', duration)
  const showSuccess = (message, duration = 2500) => showToast(message, 'success', duration)
  const showInfo = (message, duration = 2500) => showToast(message, 'info', duration)

  return {
    isGlobalLoading,
    toasts,
    showToast,
    showError,
    showSuccess,
    showInfo,
    removeToast,
  }
})

