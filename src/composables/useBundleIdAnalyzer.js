export default function useBundleIdAnalyzer() {
  // Bundle ID 质量分析器

  // 评分标准 - 更严格的评分体系
  const SCORING_CRITERIA = {
    length: {
      optimal: [20, 40], // 最佳长度范围
      acceptable: [15, 60], // 可接受范围
      weight: 0.15
    },
    readability: {
      weight: 0.25
    },
    memorability: {
      weight: 0.20
    },
    professionalism: {
      weight: 0.20
    },
    uniqueness: {
      weight: 0.10
    },
    compliance: {
      weight: 0.10
    }
  }

  // 常见的好词汇
  const POSITIVE_WORDS = [
    'pro', 'smart', 'tech', 'digital', 'modern', 'clean', 'simple',
    'secure', 'fast', 'easy', 'premium', 'elite', 'official'
  ]

  // 应该避免的词汇
  const NEGATIVE_WORDS = [
    'test', 'demo', 'temp', 'old', 'legacy', 'deprecated', 'beta'
  ]

  // 分析长度得分 - 更严格的评分
  const analyzeLengthScore = (bundleId) => {
    const length = bundleId.length
    const { optimal, acceptable } = SCORING_CRITERIA.length

    if (length >= optimal[0] && length <= optimal[1]) {
      return 90 // 降低满分，更严格
    } else if (length >= acceptable[0] && length <= acceptable[1]) {
      // 在可接受范围内，根据距离最佳范围的远近给分
      const distanceFromOptimal = Math.min(
        Math.abs(length - optimal[0]),
        Math.abs(length - optimal[1])
      )
      return Math.max(60, 90 - distanceFromOptimal * 3)
    } else if (length < acceptable[0]) {
      // 太短的惩罚更严重
      return Math.max(20, (length / acceptable[0]) * 60)
    } else {
      // 太长的惩罚更严重
      const excess = length - acceptable[1]
      return Math.max(10, 60 - excess * 2)
    }
  }

  // 分析可读性得分 - 更严格和全面的评分
  const analyzeReadabilityScore = (bundleId) => {
    let score = 80 // 起始分数降低
    const parts = bundleId.split('.')

    // 检查部分数量 - 更严格
    if (parts.length < 3) score -= 25
    if (parts.length === 3) score += 10 // 标准的三段式加分
    if (parts.length > 4) score -= 20
    if (parts.length > 5) score -= 30

    // 检查每个部分的长度和质量
    parts.forEach((part, index) => {
      if (part.length < 2) score -= 20
      if (part.length > 12) score -= 15

      // 第一部分（TLD）应该简短
      if (index === 0 && part.length > 6) score -= 10

      // 检查是否包含有意义的词汇
      if (part.length >= 3 && !/^(test|demo|temp|app\d+|example)$/.test(part)) {
        score += 5
      }
    })

    // 检查是否有数字结尾（通常不好）
    if (/\d+$/.test(bundleId)) score -= 15

    // 检查连续的相同字符
    if (/(.)\1{2,}/.test(bundleId)) score -= 20

    // 检查是否有难以发音的组合
    if (/[bcdfghjklmnpqrstvwxyz]{4,}/.test(bundleId)) score -= 10

    // 检查是否有良好的分隔
    const hasGoodSeparation = parts.every(part => part.length >= 2)
    if (hasGoodSeparation) score += 5

    return Math.max(0, Math.min(100, score))
  }

  // 分析记忆性得分 - 更注重实际记忆难度
  const analyzeMemorabilityScore = (bundleId) => {
    let score = 70 // 起始分数降低，更现实
    const parts = bundleId.split('.')

    // 检查是否包含积极词汇
    const positiveWordCount = POSITIVE_WORDS.filter(word =>
      bundleId.toLowerCase().includes(word)
    ).length
    score += positiveWordCount * 8

    // 检查是否包含消极词汇
    const negativeWordCount = NEGATIVE_WORDS.filter(word =>
      bundleId.toLowerCase().includes(word)
    ).length
    score -= negativeWordCount * 25

    // 检查长度对记忆的影响
    if (bundleId.length > 35) score -= 15
    if (bundleId.length > 50) score -= 25

    // 检查是否有重复模式（有助于记忆）
    const hasPattern = /(.{2,})\1/.test(bundleId)
    if (hasPattern) score += 10

    // 检查韵律和节奏 - 更严格
    const goodRhythmParts = parts.filter(part =>
      part.length >= 3 && part.length <= 7
    )
    score += goodRhythmParts.length * 5

    // 检查是否容易发音 - 更严格的惩罚
    const hardToPronounceParts = parts.filter(part =>
      /[bcdfghjklmnpqrstvwxyz]{4,}/.test(part) ||
      /[aeiou]{3,}/.test(part) // 连续元音也难发音
    )
    score -= hardToPronounceParts.length * 15

    // 检查数字的使用（数字通常难记）
    const numberCount = (bundleId.match(/\d/g) || []).length
    score -= numberCount * 5

    // 检查是否有意义的词汇组合
    const meaningfulParts = parts.filter(part =>
      part.length >= 3 && !/^\d+$/.test(part) &&
      !['com', 'org', 'net', 'io', 'app'].includes(part)
    )
    if (meaningfulParts.length >= 2) score += 10

    return Math.max(0, Math.min(100, score))
  }

  // 分析专业性得分 - 更严格的专业性评估
  const analyzeProfessionalismScore = (bundleId) => {
    let score = 75 // 起始分数降低
    const parts = bundleId.split('.')
    const tld = parts[0]

    // 检查TLD专业性
    const professionalTlds = ['com', 'org', 'net', 'biz', 'pro', 'corp']
    const creativeTlds = ['io', 'app', 'dev', 'tech', 'ai', 'co']
    const casualTlds = ['me', 'xyz', 'fun', 'cool']

    if (professionalTlds.includes(tld)) score += 15
    else if (creativeTlds.includes(tld)) score += 8
    else if (casualTlds.includes(tld)) score -= 10
    else score -= 5

    // 检查是否使用了行业相关词汇
    const industryWords = [
      'tech', 'digital', 'smart', 'pro', 'enterprise', 'business',
      'health', 'finance', 'edu', 'media', 'social', 'corp', 'solutions',
      'systems', 'services', 'platform', 'studio', 'group', 'labs'
    ]
    const industryWordCount = industryWords.filter(word =>
      bundleId.toLowerCase().includes(word)
    ).length
    score += industryWordCount * 8

    // 检查是否过于随意或不专业
    const unprofessionalPatterns = [
      /[xyz]{2,}/, /lol/, /wtf/, /omg/, /cool/, /awesome/, /super/,
      /\d{3,}/, /test\d*/, /demo\d*/, /temp/
    ]
    unprofessionalPatterns.forEach(pattern => {
      if (pattern.test(bundleId.toLowerCase())) score -= 20
    })

    // 检查命名规范性
    const hasProperNaming = parts.every(part =>
      /^[a-z][a-z0-9]*$/.test(part) && part.length >= 2
    )
    if (hasProperNaming) score += 10

    // 检查是否有公司/组织结构
    const hasOrgStructure = parts.length >= 3 &&
      parts.some(part => ['corp', 'inc', 'ltd', 'group', 'team'].includes(part))
    if (hasOrgStructure) score += 12

    // 检查是否避免了个人化词汇
    const personalWords = ['my', 'personal', 'private', 'home']
    const hasPersonalWords = personalWords.some(word =>
      bundleId.toLowerCase().includes(word)
    )
    if (hasPersonalWords) score -= 15

    return Math.max(0, Math.min(100, score))
  }

  // 分析唯一性得分 - 更注重真实的唯一性
  const analyzeUniquenessScore = (bundleId) => {
    let score = 80 // 起始分数降低

    // 检查是否使用了过于常见的词汇
    const commonWords = [
      'app', 'test', 'demo', 'sample', 'example', 'hello', 'world',
      'company', 'business', 'service', 'system', 'platform', 'solution'
    ]
    const commonWordCount = commonWords.filter(word =>
      bundleId.toLowerCase().includes(word)
    ).length
    score -= commonWordCount * 20

    // 检查是否有创意元素
    const creativeElements = [
      'hub', 'lab', 'studio', 'forge', 'craft', 'space', 'works',
      'labs', 'vault', 'nest', 'hive', 'core', 'edge', 'spark'
    ]
    const creativeCount = creativeElements.filter(word =>
      bundleId.toLowerCase().includes(word)
    ).length
    score += creativeCount * 12

    // 检查长度的唯一性影响
    if (bundleId.length < 20) score -= 10 // 太短可能不够独特

    // 检查是否有数字（可能降低唯一性）
    const hasNumbers = /\d/.test(bundleId)
    if (hasNumbers) {
      const numberPattern = /\d+/g
      const numbers = bundleId.match(numberPattern) || []
      // 简单数字（1,2,3等）降低唯一性
      const hasSimpleNumbers = numbers.some(num => parseInt(num) <= 10)
      if (hasSimpleNumbers) score -= 15
    }

    // 检查是否有独特的组合
    const parts = bundleId.split('.')
    const hasUniqueStructure = parts.length >= 3 &&
      parts.some(part => part.length >= 5 && !/^(com|org|net|app|tech)$/.test(part))
    if (hasUniqueStructure) score += 15

    // 检查重复模式（降低唯一性）
    if (/(.{3,})\1/.test(bundleId)) score -= 10

    return Math.max(0, Math.min(100, score))
  }

  // 分析合规性得分
  const analyzeComplianceScore = (bundleId) => {
    let score = 100

    // 检查格式合规性
    if (!/^[a-z][a-z0-9]*(\.[a-z][a-z0-9]*)*$/.test(bundleId)) {
      score = 0
      return score
    }

    // 检查长度限制
    if (bundleId.length > 100) score -= 50

    // 检查每个部分是否以字母开头
    const parts = bundleId.split('.')
    parts.forEach(part => {
      if (!/^[a-z]/.test(part)) score -= 20
    })

    // 检查是否有保留词
    const reservedWords = ['apple', 'google', 'microsoft', 'facebook', 'amazon']
    const hasReservedWords = reservedWords.some(word =>
      bundleId.toLowerCase().includes(word)
    )
    if (hasReservedWords) score -= 30

    return Math.max(0, score)
  }

  // 生成改进建议 - 更具体和实用的建议
  const generateSuggestions = (bundleId, scores) => {
    const suggestions = []
    const parts = bundleId.split('.')

    // 长度相关建议
    if (scores.length < 60) {
      if (bundleId.length < 20) {
        suggestions.push({
          type: 'length',
          message: '建议增加更多描述性组件，理想长度为20-40字符',
          priority: 'medium'
        })
      } else if (bundleId.length > 50) {
        suggestions.push({
          type: 'length',
          message: '建议缩短长度以提高可读性，避免超过50字符',
          priority: 'high'
        })
      }
    }

    // 可读性相关建议
    if (scores.readability < 70) {
      if (parts.length < 3) {
        suggestions.push({
          type: 'readability',
          message: '建议使用标准的三段式结构：com.company.app',
          priority: 'high'
        })
      }
      if (parts.some(part => part.length > 12)) {
        suggestions.push({
          type: 'readability',
          message: '建议缩短各组件长度，每部分不超过12字符',
          priority: 'medium'
        })
      }
    }

    // 记忆性相关建议
    if (scores.memorability < 60) {
      suggestions.push({
        type: 'memorability',
        message: '建议使用更有意义和容易记忆的词汇',
        priority: 'medium'
      })
      if (bundleId.length > 35) {
        suggestions.push({
          type: 'memorability',
          message: '较长的Bundle ID更难记忆，建议简化',
          priority: 'medium'
        })
      }
    }

    // 专业性相关建议
    if (scores.professionalism < 70) {
      const tld = parts[0]
      if (!['com', 'org', 'net', 'biz', 'pro'].includes(tld)) {
        suggestions.push({
          type: 'professionalism',
          message: '建议使用更专业的顶级域名如com、org或net',
          priority: 'medium'
        })
      }
      suggestions.push({
        type: 'professionalism',
        message: '建议使用更专业的术语和避免随意的词汇',
        priority: 'medium'
      })
    }

    // 唯一性相关建议
    if (scores.uniqueness < 60) {
      suggestions.push({
        type: 'uniqueness',
        message: '建议添加独特元素以提高辨识度',
        priority: 'low'
      })
    }

    // 合规性相关建议
    if (scores.compliance < 90) {
      suggestions.push({
        type: 'compliance',
        message: '请确保符合Bundle ID命名规范',
        priority: 'high'
      })
    }

    return suggestions
  }

  // 主分析函数
  const analyze = (bundleId) => {
    const scores = {
      length: analyzeLengthScore(bundleId),
      readability: analyzeReadabilityScore(bundleId),
      memorability: analyzeMemorabilityScore(bundleId),
      professionalism: analyzeProfessionalismScore(bundleId),
      uniqueness: analyzeUniquenessScore(bundleId),
      compliance: analyzeComplianceScore(bundleId)
    }

    // 计算总分
    const totalScore = Object.keys(scores).reduce((total, key) => {
      const weight = SCORING_CRITERIA[key]?.weight || 0.1
      return total + (scores[key] * weight)
    }, 0)

    // 生成等级 - 更严格的等级标准
    let grade = 'F'
    if (totalScore >= 95) grade = 'A+'
    else if (totalScore >= 90) grade = 'A'
    else if (totalScore >= 85) grade = 'A-'
    else if (totalScore >= 80) grade = 'B+'
    else if (totalScore >= 75) grade = 'B'
    else if (totalScore >= 70) grade = 'B-'
    else if (totalScore >= 65) grade = 'C+'
    else if (totalScore >= 60) grade = 'C'
    else if (totalScore >= 55) grade = 'C-'
    else if (totalScore >= 50) grade = 'D+'
    else if (totalScore >= 45) grade = 'D'
    else if (totalScore >= 40) grade = 'D-'

    const suggestions = generateSuggestions(bundleId, scores)

    return {
      bundleId,
      totalScore: Math.round(totalScore),
      grade,
      scores,
      suggestions,
      analysis: {
        length: bundleId.length,
        parts: bundleId.split('.').length,
        tld: bundleId.split('.')[0],
        hasNumbers: /\d/.test(bundleId),
        complexity: bundleId.split('.').reduce((acc, part) => acc + part.length, 0) / bundleId.split('.').length
      }
    }
  }

  // 批量分析
  const analyzeMultiple = (bundleIds) => {
    return bundleIds.map(id => analyze(id)).sort((a, b) => b.totalScore - a.totalScore)
  }

  return {
    analyze,
    analyzeMultiple
  }
}
