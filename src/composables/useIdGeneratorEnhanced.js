export default function useIdGeneratorEnhanced() {
  // 增强的Bundle ID生成器 - 更智能、更专业、更多样化

  // 顶级域名前缀策略
  const TLD_STRATEGIES = {
    formal: ['com', 'org', 'net'],
    creative: ['io', 'app', 'co', 'me', 'dev', 'tech', 'ai', 'digital'],
    marketing: ['com', 'biz', 'pro', 'info'],
    functional: ['com', 'org', 'dev', 'tech']
  }

  // 行业专用词汇库（更丰富）
  const INDUSTRY_LEXICON = {
    technology: {
      primary: ['tech', 'digital', 'smart', 'ai', 'cloud', 'data', 'code'],
      secondary: ['cyber', 'quantum', 'neural', 'pixel', 'byte', 'logic', 'sync'],
      modern: ['nexus', 'vertex', 'matrix', 'core', 'edge', 'flux', 'nova']
    },
    finance: {
      primary: ['fin', 'pay', 'bank', 'invest', 'money', 'capital'],
      secondary: ['wealth', 'trade', 'asset', 'credit', 'crypto', 'wallet'],
      modern: ['fintech', 'neobank', 'paytech', 'wealthtech', 'regtech']
    },
    healthcare: {
      primary: ['health', 'med', 'care', 'wellness', 'life', 'bio'],
      secondary: ['clinic', 'therapy', 'pharma', 'diagnose', 'heal'],
      modern: ['medtech', 'healthtech', 'biotech', 'telemedicine']
    },
    // ... 其他行业类似结构
  }

  // 地区本地化策略
  const REGION_STRATEGIES = {
    china: { 
      prefixes: ['cn', 'china'], 
      styles: ['simple', 'clean', 'pro'],
      avoid: ['complex', 'long']
    },
    japan: { 
      prefixes: ['jp', 'japan'], 
      styles: ['minimal', 'zen', 'pure'],
      prefer: ['short', 'elegant']
    },
    global: { 
      prefixes: ['global', 'world', 'international'],
      styles: ['universal', 'standard'],
      prefer: ['recognizable', 'memorable']
    }
  }

  // 智能字符串处理
  const smartClean = (str, context = 'general') => {
    let cleaned = str.toLowerCase().trim()
    
    // 移除特殊字符，保留字母数字
    cleaned = cleaned.replace(/[^\w\s]/g, '').replace(/\s+/g, '')
    
    // 移除开头的数字
    cleaned = cleaned.replace(/^[0-9]+/, '')
    
    // 根据上下文调整长度
    const maxLengths = {
      domain: 15,
      app: 12,
      component: 8,
      suffix: 6
    }
    
    const maxLength = maxLengths[context] || 12
    return cleaned.slice(0, maxLength)
  }

  // 智能域名生成
  const generateSmartDomain = (companyName, appName, industry, style) => {
    const domains = []
    
    // 1. 公司名优先策略
    if (companyName) {
      const cleanCompany = smartClean(companyName, 'domain')
      if (cleanCompany.length >= 2) {
        domains.push(cleanCompany)
        
        // 添加行业特色
        const industryData = INDUSTRY_LEXICON[industry]
        if (industryData) {
          domains.push(`${cleanCompany}${industryData.primary[0]}`)
        }
      }
    }
    
    // 2. 应用名策略
    const cleanApp = smartClean(appName, 'app')
    if (cleanApp.length >= 2) {
      domains.push(cleanApp)
      
      // 根据风格添加后缀
      if (style === 'creative') {
        const creativeSuffixes = ['lab', 'studio', 'works', 'hub', 'space']
        domains.push(`${cleanApp}${creativeSuffixes[0]}`)
      }
    }
    
    // 3. 行业主导策略
    const industryData = INDUSTRY_LEXICON[industry]
    if (industryData) {
      // 使用现代化的行业词汇
      if (industryData.modern && style === 'creative') {
        domains.push(...industryData.modern.slice(0, 2))
      } else {
        domains.push(...industryData.primary.slice(0, 2))
      }
    }
    
    return [...new Set(domains)].filter(d => d.length >= 2 && d.length <= 15)
  }

  // 智能应用标识符生成
  const generateAppIdentifiers = (appName, industry, style) => {
    const cleanApp = smartClean(appName, 'app')
    const industryData = INDUSTRY_LEXICON[industry] || { primary: ['app'], secondary: ['tool'] }
    
    // 根据风格选择词汇
    let industryWords = []
    if (style === 'creative' && industryData.modern) {
      industryWords = [...industryData.modern, ...industryData.primary]
    } else {
      industryWords = [...industryData.primary, ...industryData.secondary]
    }
    
    // 风格词汇
    const styleWords = {
      marketing: ['pro', 'plus', 'max', 'boost', 'prime', 'elite', 'premium'],
      functional: ['app', 'tool', 'kit', 'suite', 'platform', 'system'],
      formal: ['official', 'standard', 'enterprise', 'business', 'corporate'],
      creative: ['x', 'go', 'hub', 'lab', 'studio', 'space', 'forge', 'craft']
    }
    
    return {
      base: cleanApp,
      industry: industryWords.slice(0, 5),
      style: styleWords[style] || ['app'],
      variations: generateNameVariations(cleanApp)
    }
  }

  // 生成名称变体
  const generateNameVariations = (baseName) => {
    const variations = [baseName]
    
    if (baseName.length > 4) {
      // 缩写版本
      variations.push(baseName.slice(0, 4))
      variations.push(baseName.slice(0, 6))
    }
    
    // 添加常见后缀
    const suffixes = ['app', 'pro', 'go', 'hub', 'kit']
    suffixes.forEach(suffix => {
      if (baseName !== suffix) {
        variations.push(`${baseName}${suffix}`)
      }
    })
    
    return variations
  }

  // 智能地区代码生成
  const generateRegionCode = (regions, style) => {
    if (!regions || regions.length === 0) return null
    
    const regionMap = {
      global: ['global', 'world', 'intl'],
      northAmerica: ['na', 'us', 'america'],
      europe: ['eu', 'europe', 'euro'],
      asia: ['asia', 'apac', 'east'],
      china: ['cn', 'china'],
      japan: ['jp', 'japan', 'jpn'],
      southKorea: ['kr', 'korea'],
      southeastAsia: ['sea', 'asean'],
      middleEast: ['me', 'mena'],
      africa: ['af', 'africa'],
      southAmerica: ['sa', 'latam'],
      oceania: ['oc', 'oceania', 'pacific']
    }
    
    if (regions.length === 1) {
      const codes = regionMap[regions[0]] || ['global']
      return style === 'creative' ? codes[Math.floor(Math.random() * codes.length)] : codes[0]
    }
    
    // 多地区策略
    if (regions.length <= 3) {
      return regions.map(r => regionMap[r]?.[0] || r.slice(0, 2)).join('')
    }
    
    return 'multi'
  }

  // 主生成函数
  const generate = (input, count = 3, excludeIds = []) => {
    const { appName, industry, regions, companyName, style } = input
    
    if (!appName || !industry || !style) {
      return ['com.example.app', 'com.example.tool', 'com.example.platform']
    }
    
    const domains = generateSmartDomain(companyName, appName, industry, style)
    const appIds = generateAppIdentifiers(appName, industry, style)
    const regionCode = generateRegionCode(regions, style)
    const prefixes = TLD_STRATEGIES[style] || ['com']
    
    const results = []
    const usedIds = new Set(excludeIds)
    
    // 生成策略数组
    const strategies = []
    
    // 为每个域名和前缀组合生成策略
    domains.forEach(domain => {
      prefixes.forEach(prefix => {
        // 基础格式
        strategies.push(() => `${prefix}.${domain}.${appIds.base}`)
        
        // 带行业标识
        appIds.industry.forEach(ind => {
          strategies.push(() => `${prefix}.${domain}.${appIds.base}.${ind}`)
          strategies.push(() => `${prefix}.${domain}.${ind}.${appIds.base}`)
        })
        
        // 带风格标识
        appIds.style.forEach(styleWord => {
          strategies.push(() => `${prefix}.${domain}.${styleWord}.${appIds.base}`)
          strategies.push(() => `${prefix}.${domain}.${appIds.base}.${styleWord}`)
        })
        
        // 带地区代码
        if (regionCode) {
          strategies.push(() => `${prefix}.${domain}.${appIds.base}.${regionCode}`)
          strategies.push(() => `${prefix}.${domain}.${regionCode}.${appIds.base}`)
        }
        
        // 复合格式
        if (appIds.industry[0] && appIds.style[0]) {
          strategies.push(() => `${prefix}.${domain}.${appIds.industry[0]}.${appIds.base}.${appIds.style[0]}`)
        }
        
        // 使用变体名称
        appIds.variations.forEach(variant => {
          if (variant !== appIds.base) {
            strategies.push(() => `${prefix}.${domain}.${variant}`)
          }
        })
      })
    })
    
    // 随机化策略顺序以增加多样性
    const shuffledStrategies = strategies.sort(() => Math.random() - 0.5)
    
    // 生成Bundle IDs
    let attempts = 0
    while (results.length < count && attempts < shuffledStrategies.length) {
      const strategy = shuffledStrategies[attempts]
      const bundleId = strategy()
      
      if (bundleId && 
          !usedIds.has(bundleId) && 
          bundleId.length <= 100 && 
          bundleId.length >= 8 &&
          /^[a-z][a-z0-9]*(\.[a-z][a-z0-9]*)*$/.test(bundleId)) {
        results.push(bundleId)
        usedIds.add(bundleId)
      }
      attempts++
    }
    
    // 如果还需要更多结果，生成编号版本
    while (results.length < count) {
      const base = results[0] || `com.${domains[0] || 'app'}.${appIds.base}`
      const numbered = `${base}${results.length + 1}`
      if (!usedIds.has(numbered)) {
        results.push(numbered)
        usedIds.add(numbered)
      } else {
        results.push(`${base}v${results.length + 1}`)
      }
    }
    
    return results.slice(0, count)
  }
  
  return { generate }
}
