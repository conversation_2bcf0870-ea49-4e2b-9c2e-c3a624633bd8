export default function useAiGenerator() {
  const generateWithAI = async (input, count = 3) => {
    try {
      const res = await fetch('/api/ai-suggest', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ...input, count })
      });
      if (!res.ok) {
        let message = 'Request failed';
        try {
          const txt = await res.text();
          message = txt;
        } catch {}
        console.warn('AI suggest failed:', message);
        return { bundleIds: [], remaining: undefined, error: message };
      }
      const data = await res.json();
      const bundleIds = Array.isArray(data.bundleIds) ? data.bundleIds : [];
      const remaining = typeof data.remaining === 'number' ? data.remaining : undefined;
      return { bundleIds, remaining };
    } catch (e) {
      console.warn('AI suggest error:', e);
      return { bundleIds: [], remaining: undefined, error: e?.message || String(e) };
    }
  };

  return { generateWithAI };
}

