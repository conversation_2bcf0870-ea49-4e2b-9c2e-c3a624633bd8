/* 使用系统字体作为图标字体的备用方案 */
.iconfont {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  font-size: 16px;
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  display: inline-block;
  text-transform: none;
  letter-spacing: normal;
  word-wrap: normal;
  white-space: nowrap;
  direction: ltr;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* 应用图标 */
.icon-app:before {
  content: "📱";
}

/* 公司图标 */
.icon-company:before {
  content: "🏢";
}

/* 下拉箭头 */
.icon-down:before {
  content: "▼";
}

/* 勾选图标 */
.icon-check:before {
  content: "✓";
}

/* 生成图标 */
.icon-generate:before {
  content: "⚡";
}

/* 复制图标 */
.icon-copy:before {
  content: "📋";
}

/* 重新生成图标 */
.icon-refresh:before {
  content: "🔄";
}

/* 历史记录图标 */
.icon-history:before {
  content: "📜";
}

/* 清除图标 */
.icon-clear:before {
  content: "🗑️";
}

/* 设置图标 */
.icon-setting:before {
  content: "⚙️";
}
