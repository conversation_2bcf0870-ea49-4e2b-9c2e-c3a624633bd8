import { createI18n } from 'vue-i18n';
import en from './en.json';
import zh from './zh.json';
import es from './es.json';
import fr from './fr.json';
import de from './de.json';
import ja from './ja.json';

// 支持的语言列表
const supportedLanguages = ['en', 'zh', 'es', 'fr', 'de', 'ja'];

// 自动匹配浏览器语言
const getBrowserLanguage = () => {
  const browserLang = navigator.language.slice(0, 2);
  return supportedLanguages.includes(browserLang) ? browserLang : 'en';
};

const defaultLang = getBrowserLanguage();

export default createI18n({
  legacy: false,
  locale: localStorage.getItem('userLang') || defaultLang,
  fallbackLocale: 'en',
  messages: { en, zh, es, fr, de, ja }
});
