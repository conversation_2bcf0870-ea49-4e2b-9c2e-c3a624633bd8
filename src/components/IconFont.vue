<template>
  <i 
    :class="iconClass" 
    :style="iconStyle"
    @click="$emit('click')"
  ></i>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  name: {
    type: String,
    required: true
  },
  size: {
    type: [String, Number],
    default: '16px'
  },
  color: {
    type: String,
    default: 'currentColor'
  }
})

defineEmits(['click'])

const iconClass = computed(() => {
  return `iconfont icon-${props.name}`
})

const iconStyle = computed(() => {
  return {
    fontSize: typeof props.size === 'number' ? `${props.size}px` : props.size,
    color: props.color,
    cursor: 'inherit'
  }
})
</script>

<style scoped>
.iconfont {
  display: inline-block;
  font-style: normal;
  vertical-align: baseline;
  text-align: center;
  text-transform: none;
  line-height: 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>
