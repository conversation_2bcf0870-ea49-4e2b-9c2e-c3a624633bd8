<template>
  <div class="toasts-container" aria-live="polite" aria-atomic="true">
    <TransitionGroup name="toast" tag="div">
      <div v-for="t in ui.toasts" :key="t.id" class="toast" :class="t.type" role="status">
        <span class="icon" aria-hidden="true">{{ iconFor(t.type) }}</span>
        <span class="message">{{ t.message }}</span>
        <button class="close" @click="ui.removeToast(t.id)" aria-label="Close">×</button>
      </div>
    </TransitionGroup>
  </div>
</template>

<script setup>
import { useUiStore } from '@/stores/ui'

const ui = useUiStore()

const iconFor = (type) => {
  if (type === 'success') return '✅'
  if (type === 'error') return '⚠️'
  return 'ℹ️'
}
</script>

<style scoped>
.toasts-container {
  position: fixed;
  top: 80px;
  right: 16px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.toast-enter-active, .toast-leave-active {
  transition: all 0.25s ease;
}
.toast-enter-from, .toast-leave-to {
  opacity: 0;
  transform: translateY(-8px);
}

.toast {
  display: flex;
  align-items: center;
  gap: 8px;
  max-width: 320px;
  padding: 10px 12px;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.3);
  border: 1px solid rgba(255,255,255,0.15);
  background: rgba(30, 41, 59, 0.95);
  color: #e5e7eb;
}
.toast .icon { font-size: 14px; }
.toast .message { flex: 1; font-size: 14px; line-height: 1.3; }
.toast .close { background: transparent; color: #e5e7eb; border: none; cursor: pointer; font-size: 16px; }

.toast.info { border-left: 3px solid #3b82f6; }
.toast.success { border-left: 3px solid #10b981; }
.toast.error { border-left: 3px solid #ef4444; }
</style>

