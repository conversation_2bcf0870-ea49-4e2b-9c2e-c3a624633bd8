<template>
  <div class="language-switcher" v-click-outside="closeDropdown">
    <div class="language-selector" @click="toggleDropdown">
      <div class="current-language">
        <span class="language-flag">{{ getLanguageFlag(locale) }}</span>
        <span class="language-name">{{ getLanguageName(locale) }}</span>
        <IconFont name="down" size="16" class="dropdown-icon" :class="{ 'open': isOpen }" />
      </div>
    </div>

    <Transition name="dropdown">
      <div v-if="isOpen" class="dropdown-container">
        <!-- 移动端遮罩层 -->
        <div class="dropdown-overlay" @click="closeDropdown"></div>

        <div class="language-dropdown">
          <div
            v-for="lang in languages"
            :key="lang.code"
            class="language-option"
            :class="{ 'active': lang.code === locale }"
            @click="selectLanguage(lang.code)"
          >
            <span class="language-flag">{{ lang.flag }}</span>
            <span class="language-name">{{ lang.name }}</span>
            <IconFont v-if="lang.code === locale" name="check" size="16" class="check-icon" />
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import IconFont from './IconFont.vue'

const { locale, t } = useI18n()
const isOpen = ref(false)

// 语言配置
const languages = computed(() => [
  { code: 'en', name: t('language.english'), flag: '🇺🇸' },
  { code: 'zh', name: t('language.chinese'), flag: '🇨🇳' },
  { code: 'es', name: t('language.spanish'), flag: '🇪🇸' },
  { code: 'fr', name: t('language.french'), flag: '🇫🇷' },
  { code: 'de', name: t('language.german'), flag: '🇩🇪' },
  { code: 'ja', name: t('language.japanese'), flag: '🇯🇵' }
])

// 获取语言旗帜
const getLanguageFlag = (langCode) => {
  const lang = languages.value.find(l => l.code === langCode)
  return lang ? lang.flag : '🌍'
}

// 获取语言名称
const getLanguageName = (langCode) => {
  const lang = languages.value.find(l => l.code === langCode)
  return lang ? lang.name : 'English'
}

// 切换下拉菜单
const toggleDropdown = () => {
  isOpen.value = !isOpen.value
}

// 关闭下拉菜单
const closeDropdown = () => {
  isOpen.value = false
}

// 选择语言
const selectLanguage = (langCode) => {
  locale.value = langCode
  localStorage.setItem('userLang', langCode)
  closeDropdown()
}

// 点击外部关闭指令
const vClickOutside = {
  mounted(el, binding) {
    el.clickOutsideEvent = (event) => {
      if (!(el === event.target || el.contains(event.target))) {
        binding.value()
      }
    }
    document.addEventListener('click', el.clickOutsideEvent)
  },
  unmounted(el) {
    document.removeEventListener('click', el.clickOutsideEvent)
  }
}
</script>

<style scoped>
.language-switcher {
  position: relative;
  z-index: 1000;
}

.language-selector {
  cursor: pointer;
}

.current-language {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border: 1px solid rgba(71, 85, 105, 0.5);
  border-radius: 8px;
  background: rgba(51, 65, 85, 0.8);
  backdrop-filter: blur(10px);
  color: #f8fafc;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  min-width: 120px;
}

.current-language:hover {
  border-color: #3b82f6;
  background: rgba(51, 65, 85, 0.9);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.language-flag {
  font-size: 1.125rem;
  line-height: 1;
}

.language-name {
  flex: 1;
  font-weight: 500;
}

.dropdown-icon {
  color: #94a3b8;
  transition: transform 0.2s ease;
}

.dropdown-icon.open {
  transform: rotate(180deg);
}

.dropdown-container {
  position: relative;
}

.dropdown-overlay {
  display: none;
}

.language-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  margin-top: 0.5rem;
  background: rgba(30, 41, 59, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  z-index: 1001;
}

.language-option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid rgba(71, 85, 105, 0.2);
}

.language-option:last-child {
  border-bottom: none;
}

.language-option:hover {
  background: rgba(59, 130, 246, 0.1);
}

.language-option.active {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

.language-option .language-flag {
  font-size: 1.125rem;
}

.language-option .language-name {
  flex: 1;
  font-weight: 500;
  color: #e2e8f0;
}

.language-option.active .language-name {
  color: #3b82f6;
  font-weight: 600;
}

.check-icon {
  color: #3b82f6;
  flex-shrink: 0;
}

/* 下拉动画 */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: all 0.3s ease;
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: translateY(-8px) scale(0.95);
}

.dropdown-enter-to,
.dropdown-leave-from {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* 移动端动画 */
@media (max-width: 480px) {
  .dropdown-enter-from,
  .dropdown-leave-to {
    opacity: 0;
  }

  .dropdown-enter-from .dropdown-overlay,
  .dropdown-leave-to .dropdown-overlay {
    opacity: 0;
  }

  .dropdown-enter-from .language-dropdown,
  .dropdown-leave-to .language-dropdown {
    transform: scale(0.9) translateY(20px);
  }

  .dropdown-enter-to,
  .dropdown-leave-from {
    opacity: 1;
  }

  .dropdown-enter-to .dropdown-overlay,
  .dropdown-leave-from .dropdown-overlay {
    opacity: 1;
  }

  .dropdown-enter-to .language-dropdown,
  .dropdown-leave-from .language-dropdown {
    transform: scale(1) translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .language-switcher {
    position: relative;
    z-index: 1001;
  }

  .current-language {
    min-width: 80px;
    padding: 0.5rem 0.75rem;
    justify-content: center;
  }

  .current-language .language-name {
    display: none;
  }

  .language-dropdown {
    position: fixed;
    top: auto;
    bottom: 100%;
    right: 0;
    left: auto;
    margin-top: 0;
    margin-bottom: 0.5rem;
    min-width: 180px;
    max-height: 300px;
    overflow-y: auto;
  }

  .language-option {
    padding: 1rem;
  }

  .language-option .language-name {
    display: block;
    font-size: 1rem;
  }

  .language-option .language-flag {
    font-size: 1.25rem;
  }
}

@media (max-width: 480px) {
  .current-language {
    min-width: 60px;
    padding: 0.5rem;
  }

  .dropdown-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .dropdown-overlay {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
  }

  .language-dropdown {
    position: relative;
    top: auto;
    left: auto;
    right: auto;
    bottom: auto;
    transform: none;
    margin: 0;
    min-width: 240px;
    max-width: 90vw;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    z-index: 1;
  }
}
</style>
