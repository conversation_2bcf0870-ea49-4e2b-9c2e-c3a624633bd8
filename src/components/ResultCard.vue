<template>
  <div class="result-card">
    <div class="result-header">
      <h3>{{ $t('results.title') }}</h3>
      <button @click="handleRegenerate" class="regenerate-button">
        {{ $t('results.regenerate') }}
      </button>
    </div>

    <div class="results-list">
      <div
        v-for="(result, index) in results"
        :key="index"
        class="result-item"
      >
        <div class="result-content">
          <code class="bundle-id">{{ result }}</code>
          <button
            @click="copyToClipboard(result, index)"
            class="copy-button"
            :class="{ 'copied': copiedIndex === index }"
          >
            {{ copiedIndex === index ? $t('results.copied') : $t('results.copy') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const props = defineProps({
  results: {
    type: Array,
    required: true
  }
})

const emit = defineEmits(['regenerate'])

const copiedIndex = ref(-1)

const copyToClipboard = async (text, index) => {
  try {
    await navigator.clipboard.writeText(text)
    copiedIndex.value = index
    setTimeout(() => {
      copiedIndex.value = -1
    }, 2000)
  } catch (err) {
    console.error('Failed to copy text: ', err)
    // Fallback for older browsers
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)

    copiedIndex.value = index
    setTimeout(() => {
      copiedIndex.value = -1
    }, 2000)
  }
}

const handleRegenerate = () => {
  emit('regenerate')
}
</script>

<style scoped>
.result-card {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(59, 130, 246, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  padding: 1.5rem;
  margin-top: 2rem;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(59, 130, 246, 0.3);
}

.result-header h3 {
  color: #f8fafc;
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.regenerate-button {
  background: linear-gradient(135deg, #10b981 0%, #06d6a0 100%);
  color: white;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.regenerate-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.result-item {
  border: 1px solid rgba(71, 85, 105, 0.5);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.2s ease;
  background: rgba(51, 65, 85, 0.3);
}

.result-item:hover {
  border-color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.result-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: rgba(51, 65, 85, 0.4);
}

.bundle-id {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  color: #e2e8f0;
  background: rgba(15, 23, 42, 0.8);
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  border: 1px solid rgba(59, 130, 246, 0.3);
  flex: 1;
  margin-right: 1rem;
  word-break: break-all;
}

.copy-button {
  background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%);
  color: white;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.copy-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.copy-button.copied {
  background: linear-gradient(135deg, #10b981 0%, #06d6a0 100%);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.copy-button.copied:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

@media (max-width: 768px) {
  .result-card {
    margin: 1rem;
    padding: 1rem;
  }

  .result-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .result-content {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }

  .bundle-id {
    margin-right: 0;
    text-align: center;
  }

  .copy-button {
    width: 100%;
  }
}
</style>
