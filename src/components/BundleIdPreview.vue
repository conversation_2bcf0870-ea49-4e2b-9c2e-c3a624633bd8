<template>
  <div class="bundle-preview">
    <div class="preview-header">
      <h3>{{ $t('preview.title') }}</h3>
      <div class="preview-controls">
        <button @click="togglePreview" class="toggle-button">
          {{ showPreview ? $t('preview.hidePreview') : $t('preview.showPreview') }}
        </button>
      </div>
    </div>

    <div v-if="showPreview" class="preview-content">
      <!-- 实时生成预览 -->
      <div v-if="previewIds.length > 0" class="preview-section">
        <h4>{{ $t('preview.basedOnInput') }}</h4>
        <div class="preview-list">
          <div v-for="(id, index) in previewIds" :key="index" class="preview-item">
            <code class="preview-bundle-id">{{ id }}</code>
            <div class="preview-score" :class="getScoreClass(getQuickScore(id))">
              {{ getQuickScore(id) }}
            </div>
          </div>
        </div>
      </div>

      <!-- 输入验证 -->
      <div class="validation-section">
        <h4>{{ $t('preview.inputValidation') }}</h4>
        <div class="validation-grid">
          <div class="validation-item" :class="{ 'valid': validations.appName, 'invalid': !validations.appName }">
            <span class="validation-icon">{{ validations.appName ? '✓' : '✗' }}</span>
            <span class="validation-text">{{ $t('preview.appName') }}</span>
          </div>
          <div class="validation-item" :class="{ 'valid': validations.industry, 'invalid': !validations.industry }">
            <span class="validation-icon">{{ validations.industry ? '✓' : '✗' }}</span>
            <span class="validation-text">{{ $t('preview.industrySelection') }}</span>
          </div>
          <div class="validation-item" :class="{ 'valid': validations.regions, 'invalid': !validations.regions }">
            <span class="validation-icon">{{ validations.regions ? '✓' : '✗' }}</span>
            <span class="validation-text">{{ $t('preview.regionSelection') }}</span>
          </div>
          <div class="validation-item" :class="{ 'valid': validations.style, 'invalid': !validations.style }">
            <span class="validation-icon">{{ validations.style ? '✓' : '✗' }}</span>
            <span class="validation-text">{{ $t('preview.stylePreference') }}</span>
          </div>
        </div>
      </div>

      <!-- 生成建议 -->
      <div v-if="suggestions.length > 0" class="suggestions-section">
        <h4>{{ $t('preview.optimizationTips') }}</h4>
        <div class="suggestion-list">
          <div v-for="(suggestion, index) in suggestions" :key="index" class="suggestion-item">
            <span class="suggestion-icon">💡</span>
            <span class="suggestion-text">{{ suggestion }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import useIdGeneratorEnhanced from '@/composables/useIdGeneratorEnhanced'
import useBundleIdAnalyzer from '@/composables/useBundleIdAnalyzer'

const { t } = useI18n()

const { generate } = useIdGeneratorEnhanced()
const { analyze } = useBundleIdAnalyzer()

const props = defineProps({
  formData: {
    type: Object,
    required: true
  }
})

const showPreview = ref(false)
const previewIds = ref([])

// 验证状态
const validations = computed(() => ({
  appName: props.formData.appName && props.formData.appName.trim().length >= 2,
  industry: props.formData.industry && props.formData.industry.length > 0,
  regions: props.formData.regions && props.formData.regions.length > 0,
  style: props.formData.style && props.formData.style.length > 0
}))

// 生成建议
const suggestions = computed(() => {
  const suggestions = []

  if (!validations.value.appName) {
    suggestions.push(t('preview.appNameTooShort'))
  } else if (props.formData.appName.length > 20) {
    suggestions.push(t('preview.appNameTooLong'))
  }

  if (!validations.value.industry) {
    suggestions.push(t('preview.selectIndustry'))
  }

  if (!validations.value.regions) {
    suggestions.push(t('preview.selectRegions'))
  } else if (props.formData.regions.length > 3) {
    suggestions.push(t('preview.tooManyRegions'))
  }

  if (!validations.value.style) {
    suggestions.push(t('preview.selectStyle'))
  }

  if (props.formData.companyName && props.formData.companyName.length > 15) {
    suggestions.push(t('preview.companyNameTooLong'))
  }

  return suggestions
})

// 监听表单数据变化，实时生成预览
watch(() => props.formData, () => {
  if (showPreview.value && isFormValid()) {
    generatePreview()
  }
}, { deep: true })

// 检查表单是否有效
const isFormValid = () => {
  return validations.value.appName &&
         validations.value.industry &&
         validations.value.regions &&
         validations.value.style
}

// 生成预览
const generatePreview = () => {
  if (!isFormValid()) {
    previewIds.value = []
    return
  }

  try {
    const generated = generate(props.formData, 3)
    previewIds.value = generated
  } catch (error) {
    console.error('Preview generation failed:', error)
    previewIds.value = []
  }
}

// 快速评分
const getQuickScore = (bundleId) => {
  try {
    const analysis = analyze(bundleId)
    return analysis.totalScore
  } catch (error) {
    return 50
  }
}

// 获取分数样式类
const getScoreClass = (score) => {
  if (score >= 80) return 'score-excellent'
  if (score >= 70) return 'score-good'
  if (score >= 60) return 'score-fair'
  return 'score-poor'
}

// 切换预览显示
const togglePreview = () => {
  showPreview.value = !showPreview.value
  if (showPreview.value && isFormValid()) {
    generatePreview()
  }
}
</script>

<style scoped>
.bundle-preview {
  background: rgba(30, 41, 59, 0.6);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  border: 1px solid rgba(139, 92, 246, 0.3);
  padding: 1.5rem;
  margin-top: 1.5rem;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.preview-header h3 {
  color: #f8fafc;
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
}

.toggle-button {
  background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
  color: white;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.toggle-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.preview-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.preview-section h4,
.validation-section h4,
.suggestions-section h4 {
  color: #f8fafc;
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 500;
}

.preview-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: rgba(51, 65, 85, 0.4);
  border-radius: 8px;
  border: 1px solid rgba(71, 85, 105, 0.3);
}

.preview-bundle-id {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  color: #e2e8f0;
  background: rgba(15, 23, 42, 0.6);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  flex: 1;
  margin-right: 0.75rem;
}

.preview-score {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  min-width: 2.5rem;
  text-align: center;
}

.score-excellent { background: #10b981; color: white; }
.score-good { background: #3b82f6; color: white; }
.score-fair { background: #f59e0b; color: white; }
.score-poor { background: #ef4444; color: white; }

.validation-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.75rem;
}

.validation-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.validation-item.valid {
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.validation-item.invalid {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.validation-icon {
  font-weight: bold;
  font-size: 1rem;
}

.validation-item.valid .validation-icon {
  color: #10b981;
}

.validation-item.invalid .validation-icon {
  color: #ef4444;
}

.validation-text {
  color: #e2e8f0;
}

.suggestion-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 6px;
  font-size: 0.875rem;
}

.suggestion-text {
  color: #e2e8f0;
}

@media (max-width: 768px) {
  .bundle-preview {
    margin: 1rem;
    padding: 1rem;
  }

  .preview-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .validation-grid {
    grid-template-columns: 1fr;
  }

  .preview-item {
    flex-direction: column;
    gap: 0.5rem;
    align-items: stretch;
  }

  .preview-bundle-id {
    margin-right: 0;
    text-align: center;
  }
}
</style>
