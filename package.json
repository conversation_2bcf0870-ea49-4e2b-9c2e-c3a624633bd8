{"name": "bundle-generator", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix", "deploy": "npm run build && wrangler pages deploy dist", "deploy:preview": "npm run build && wrangler pages deploy dist --compatibility-date=2024-01-01"}, "dependencies": {"pinia": "^3.0.3", "vue": "^3.5.18", "vue-i18n": "^11.1.11", "vue-router": "^4.5.1"}, "devDependencies": {"@eslint/js": "^9.31.0", "@vitejs/plugin-vue": "^6.0.1", "eslint": "^9.31.0", "eslint-plugin-vue": "~10.3.0", "globals": "^16.3.0", "terser": "^5.43.1", "vite": "^7.0.6", "vite-plugin-vue-devtools": "^8.0.0"}}