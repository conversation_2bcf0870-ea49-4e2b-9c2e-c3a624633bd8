# Bundle ID Generator

一个专业的 Bundle ID 生成器，帮助开发者为移动应用生成符合规范的 Bundle ID。

## 功能特性

- 🎯 **智能生成**: 根据应用名称、行业、地区和风格生成专业的 Bundle ID
- 🌍 **多语言支持**: 支持中文和英文，自动检测浏览器语言
- 📱 **响应式设计**: 完美适配桌面端和移动端
- 💾 **历史记录**: 本地缓存生成历史，支持搜索和导出
- 🔄 **批量生成**: 每次生成3个不同的 Bundle ID 供选择
- 📋 **一键复制**: 快速复制生成的 Bundle ID
- 🎨 **现代UI**: 美观的渐变背景和流畅的动画效果

## 支持的配置

### 行业类型
- 科技/Technology
- 金融/Finance
- 医疗健康/Healthcare
- 教育/Education
- 娱乐/Entertainment
- 电商/E-commerce
- 旅游/Travel
- 餐饮/Food & Beverage
- 健身运动/Fitness & Health
- 社交媒体/Social Media
- 效率工具/Productivity
- 游戏/Games
- 新闻媒体/News & Media
- 商务/Business
- 生活方式/Lifestyle

### 目标地区
- 全球/Global
- 北美/North America
- 欧洲/Europe
- 亚洲/Asia
- 中国/China
- 日本/Japan
- 韩国/South Korea
- 东南亚/Southeast Asia
- 中东/Middle East
- 非洲/Africa
- 南美/South America
- 大洋洲/Oceania

### 风格倾向
- 偏营销/Marketing-focused
- 偏功能/Function-focused
- 正式/Formal & Professional
- 创意简约/Creative & Minimalist

## 技术栈

- **前端框架**: Vue 3 + Composition API
- **构建工具**: Vite
- **状态管理**: Pinia
- **路由**: Vue Router
- **国际化**: Vue I18n
- **样式**: CSS3 + Flexbox/Grid
- **部署**: Cloudflare Pages

## 开发环境设置

### 推荐的 IDE 配置

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (请禁用 Vetur)

### 项目安装

```sh
npm install
```

### 开发服务器

```sh
npm run dev
```

### 生产构建

```sh
npm run build
```

### 代码检查

```sh
npm run lint
```

### 预览构建结果

```sh
npm run preview
```

## 部署

项目已配置为可以轻松部署到 Cloudflare Pages。详细部署说明请参考 [DEPLOYMENT.md](./DEPLOYMENT.md)。

### 快速部署

1. 将代码推送到 GitHub
2. 在 Cloudflare Pages 中连接仓库
3. 设置构建命令: `npm run build`
4. 设置输出目录: `dist`
5. 部署完成！

## 项目结构

```
src/
├── components/          # 可复用组件
│   ├── BundleIdForm.vue    # 表单组件
│   ├── ResultCard.vue      # 结果展示组件
│   ├── HistoryList.vue     # 历史记录组件
│   └── LanguageSwitcher.vue # 语言切换组件
├── composables/         # 组合式函数
│   └── useIdGenerator.js   # Bundle ID 生成逻辑
├── locales/            # 国际化文件
│   ├── en.json            # 英文翻译
│   ├── zh.json            # 中文翻译
│   └── i18n.js            # i18n 配置
├── stores/             # Pinia 状态管理
│   └── history.js         # 历史记录状态
├── views/              # 页面组件
│   └── GeneratorView.vue  # 主页面
├── router/             # 路由配置
│   └── index.js
├── App.vue             # 根组件
└── main.js             # 应用入口
```

## 贡献

欢迎提交 Issue 和 Pull Request！

## 许可证

MIT License
