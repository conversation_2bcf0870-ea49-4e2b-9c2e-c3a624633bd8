/* Cloudflare Pages Function: /api/ai-suggest
 * - Keeps OpenAI API key secret on server
 * - Simple per-IP rate limit (KV-backed if available; in-memory fallback)
 * - Returns a JSON array of bundle IDs suggested by the model
 */

const DEFAULT_MODEL = 'gpt-4o-mini';
const DEFAULT_BASE_URL = 'https://aihubmix.com/v1';
const MAX_COUNT = 5;
const DEFAULT_COUNT = 3;
const HOURLY_LIMIT = 50; // per IP per hour

// In-memory fallback store for rate limiting (per worker instance, non-durable)
const memoryRateStore = new Map();

function hourBucket(ts = Date.now()) {
  const d = new Date(ts);
  d.setMinutes(0, 0, 0);
  return d.toISOString();
}

async function checkAndIncRateLimit(env, ip) {
  const bucket = hourBucket();
  const key = `rl:${ip}:${bucket}`;

  if (env.RATE_LIMIT) {
    // KV-backed counter with 1h TTL from now
    const current = parseInt((await env.RATE_LIMIT.get(key)) || '0', 10);
    if (current >= HOURLY_LIMIT) return { allowed: false, remaining: 0 };
    await env.RATE_LIMIT.put(key, String(current + 1), { expirationTtl: 60 * 60 });
    return { allowed: true, remaining: Math.max(0, HOURLY_LIMIT - (current + 1)) };
  }

  // Fallback: in-memory (non-persistent)
  const rec = memoryRateStore.get(key) || { count: 0, expiresAt: Date.now() + 3600_000 };
  if (Date.now() > rec.expiresAt) {
    memoryRateStore.delete(key);
    memoryRateStore.set(key, { count: 1, expiresAt: Date.now() + 3600_000 });
    return { allowed: true, remaining: HOURLY_LIMIT - 1 };
  }
  if (rec.count >= HOURLY_LIMIT) return { allowed: false, remaining: 0 };
  rec.count += 1;
  memoryRateStore.set(key, rec);
  return { allowed: true, remaining: Math.max(0, HOURLY_LIMIT - rec.count) };
}

function buildPrompt(input) {
  const { appName, industry, regions = [], companyName = '', style } = input;
  const safe = (v) => (typeof v === 'string' ? v : Array.isArray(v) ? v.join(', ') : '');

  return `你是一个资深移动应用命名顾问，负责基于输入生成专业且合规的 iOS/Android Bundle ID 候选项。\n\n要求：\n- 严格遵守正则：^[a-z][a-z0-9]*(.[a-z][a-z0-9]*)*$\n- 每条长度 8-100 字符，使用小写和点分段；每段 2-12 字符，且以字母开头\n- 倾向 com/org/net（专业）或与风格相符的 TLD\n- 结合行业、公司、地区与风格，输出更有辨识度的候选\n- 只输出 JSON 对象，字段 bundle_ids: string[]，不要多余文本\n\n输入：\n- appName: ${safe(appName)}\n- companyName: ${safe(companyName)}\n- industry: ${safe(industry)}\n- regions: ${safe(regions)}\n- style: ${safe(style)}\n`;
}

function validateBundleId(id) {
  return (
    typeof id === 'string' &&
    id.length >= 8 &&
    id.length <= 100 &&
    /^[a-z][a-z0-9]*(\.[a-z][a-z0-9]*)*$/.test(id)
  );
}

export async function onRequestPost({ request, env }) {
  try {
    const ip = request.headers.get('CF-Connecting-IP') || 'unknown';
    const { allowed, remaining } = await checkAndIncRateLimit(env, ip);
    if (!allowed) {
      return new Response(JSON.stringify({ error: 'rate_limited', message: 'Too many requests. Try again later.' }), {
        status: 429,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const body = await request.json().catch(() => ({}));
    const { appName, industry, regions, companyName, style } = body || {};
    let { count } = body || {};

    if (!appName || !industry || !style) {
      return new Response(JSON.stringify({ error: 'invalid_input', message: 'appName, industry, style are required' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    count = Math.min(Math.max(parseInt(count || DEFAULT_COUNT, 10), 1), MAX_COUNT);

    const apiKey = env.OPENAI_API_KEY;
    if (!apiKey) {
      return new Response(JSON.stringify({ error: 'missing_key', message: 'OPENAI_API_KEY is not configured on server' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const model = env.OPENAI_MODEL || DEFAULT_MODEL;
    const systemPrompt = 'You are a helpful assistant that returns only a strict JSON object.';
    const userPrompt = buildPrompt({ appName, industry, regions, companyName, style }) + `\n请严格返回：{ "bundle_ids": string[] }，数组长度为 ${count}`;

    const base = (env.OPENAI_BASE_URL || DEFAULT_BASE_URL).replace(/\/+$/, '');
    const resp = await fetch(`${base}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        response_format: { type: 'json_object' },
        temperature: 0.7
      })
    });

    if (!resp.ok) {
      const text = await resp.text();
      return new Response(JSON.stringify({ error: 'openai_error', message: text }), {
        status: 502,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const data = await resp.json();
    const content = data?.choices?.[0]?.message?.content || '{}';
    let parsed;
    try {
      parsed = JSON.parse(content);
    } catch {
      parsed = { bundle_ids: [] };
    }

    // Sanitize output
    const unique = new Set();
    const list = Array.isArray(parsed.bundle_ids) ? parsed.bundle_ids : [];
    const cleaned = list
      .filter(validateBundleId)
      .filter((id) => (unique.has(id) ? false : unique.add(id)));

    return new Response(JSON.stringify({ bundleIds: cleaned.slice(0, count), remaining }), {
      status: 200,
      headers: { 'Content-Type': 'application/json', 'Cache-Control': 'no-store' }
    });
  } catch (err) {
    return new Response(JSON.stringify({ error: 'server_error', message: String(err?.message || err) }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

